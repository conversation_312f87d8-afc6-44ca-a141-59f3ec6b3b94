<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Exception;
use Illuminate\Http\Request;
use PAMI\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Action\OriginateAction;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Response\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Log as UserLog;
use PAMI\Message\Event\OriginateResponseEvent;

class CallController extends Controller
{
    private string $recordingPath = '/var/lib/asterisk/sounds/en/robocall_verification/';
    private string $startRec = 'robocall_verification/enter_verification_code';
    private string $recEnd = 'robocall_verification/successfully_verify';
    private string $recInvalid = 'robocall_verification/invalid_otp';
    private string $trunk = 'HostedFET184';
    private string $callerId = '2138343002';
    private string $context = 'soneri_robo_call';

    private array $managerOptions = [
        'host' => '127.0.0.1',
        'username' => 'defaultapp',
        'secret' => 'randomsecretstring',
        'scheme' => 'tcp://',
        'port' => 5038,
        'connect_timeout' => 10000,
        'read_timeout' => 10000
    ];

    /**
     * @throws \PAMI\Client\Exception\ClientException
     */

    public function makeCall(Request $request)
    {
        $request->validate([
            'number' => 'required',
            'stan' => 'required|numeric|digits:6',
            'rrn' => 'nullable|numeric|digits:12',
            'ipaddress' => 'nullable|string|max:50|regex:/^[a-zA-Z0-9~!@#$%^&*()_+=\-\[\]{};:\'",.<>?\\/` ]*$/u',
            'macaddress' => 'nullable|string|max:100|regex:/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/',
            'devicename' => 'nullable|string|max:200',
            'location' => 'nullable|string|max:300',
            'sblusername' => 'required|string|max:50',
            'channel' => 'required|string|max:50',
            'legalidentityvalue' => 'required|string|max:35',
            'customerid' => 'nullable|string|max:50',
        ]);
        $client = new ClientImpl($this->managerOptions);

        $action = new OriginateAction("PJSIP/{$request->number}@$this->trunk");
        $action->setAsync(false);
        $action->setCallerId($this->callerId);
        $action->setApplication('AGI');
        $action->setData('agi:async');

        $hangup = false;
        $answered = false;

        $client->registerEventListener(function (EventMessage $eventMessage) use ($client, &$hangup, &$answered, $request) {
            if ($eventMessage instanceof OriginateResponseEvent) {
                return response()->json([
                    'event' => 'OriginateResponse',
                    'uniqueid' => $eventMessage->getKey('Uniqueid'),
                    'channel' => $eventMessage->getKey('Channel'),
                    'callerid' => $eventMessage->getKey('CallerIDNum'),
                    'response' => $eventMessage->getKey('Response'),
                ]);
            }

            if ($eventMessage instanceof AsyncAGIStartEvent) {
                Log::info("Processing AsyncAGIStartEvent");
                try {
                    $agiClient = new AsyncClientImpl([
                        'pamiClient' => $client,
                        'asyncAgiEvent' => $eventMessage
                    ]);

                    $agiClient->answer();
                    $answered = true;
                    sleep(2);
                    Log::info("call answered.");
                    $agiClient->streamFile("robocall_verification/greeting-soneri");
                    sleep(1);
                    Log::info("verification intro");

                    Log::info("Waiting for birth date input - first attempt");
                    $playBtdResult = $agiClient->getData("robocall_verification/dob-format", 7000, 8);
                    $inputBtdCode = $playBtdResult->getDigits();

                    if (empty($inputBtdCode)) {
                        Log::info("No input received, replaying prompt");
                        $playBtdResult = $agiClient->getData("robocall_verification/dob-format", 7000, 8);
                        $inputBtdCode = $playBtdResult->getDigits();

                        if (empty($inputBtdCode)) {
                            Log::info("No input received after second prompt");
                            $agiClient->streamFile("robocall_verification/no-input-received");
                            $inputBtdCode = "";

                            $agiClient->hangup();
                            $hangup = true;

                            return response()->json([
                                'event' => 'AsyncAGIStart',
                                'channel' => $eventMessage->getKey('Channel'),
                                'uniqueid' => $eventMessage->getKey('Uniqueid'),
                            ]);
                        }
                    }


                    $data = [
                        'stan' => $request->stan,
                        'rrn' => $request->rrn ?? '',
                        'ipaddress' => $request->ipaddress ?? '',
                        'macaddress' => $request->macaddress ?? '',
                        'devicename' => $request->devicename ?? '',
                        'lattitude' => $request->lattitude ?? '',
                        'longitude' => $request->longitude ?? '',
                        'session' => $request->session ?? '',
                        'location' => $request->location ?? '',
                        'datetime' => Carbon::now(),
                        'sblusername' => $request->sblusername,
                        'channel' => $request->channel,
                        'legalidentityvalue' => $request->legalidentityvalue,
                        'customerid' => $request->customerid ?? '',
                        'mobile' => $request->number,
                        'dob' => $inputBtdCode,
                    ];
                    Log::info("data: " . json_encode($data));


                    $log = new UserLog();
                    $log->unique_id = $eventMessage->getKey('Uniqueid');
                    $log->number = $request->number;
                    $log->call_time = now();
                    $log->data = json_encode($data);
                    $log->dtmf = $inputBtdCode;

                    $response = Http::withOptions(['verify' => false])->withHeaders([
                        'username' => 'API_SONERI_TELECARD',
                        'password' => 'vaAYQGSsjdZO+qJuSkzCKA==',
                        'Content-Type' => 'application/json',
                    ])->timeout(120)->post('https://172.20.31.43:7843/rest_robocall/v1/verifydob', $data);
                    Log::info("after api hit.");
                    Log::info($response);

                    if ($response->failed()) {
                        Log::info("API request failed");
                        $log->result = "Failed";
                        $log->save();
                        $agiClient->hangup();
                        $hangup = true;
                    } else if ($response->successful() && $response->json()['responsecode'] === '00') {
                        Log::info("Verification successful");
                        $agiClient->streamFile("robocall_verification/successfully_verify");
                        $log->result = "Successfull";
                        $log->save();
                    } else {
                        Log::info("Invalid birthdate");
                        $log->result = "Invalid Birthdate";
                        $log->save();
                        $agiClient->streamFile("robocall_verification/invalid-birthdate");
                    }
                    Log::info("Hanging up call");
                    $agiClient->hangup();
                    $hangup = true;

                    return response()->json([
                        'event' => 'AsyncAGIStart',
                        'channel' => $eventMessage->getKey('Channel'),
                        'uniqueid' => $eventMessage->getKey('Uniqueid'),
                    ]);
                } catch (\Exception $e) {
                    Log::error("Call error: " . $e->getMessage());
                    // $answered = false;
                    $hangup = true;
                    return response()->json(['message' => 'Hangup call before verification', 'error' => $e->getMessage()], 401);
                }
            }

            if (strpos(get_class($eventMessage), 'HangupEvent') !== false) {
                Log::info('Call was hung up by user');
                $hangup = true;
                // $answered = false;
                return response()->json(['event' => 'Hangup', 'message' => 'Call was hung up by user']);
            }

            if (get_class($eventMessage) === 'PAMI\Message\Event\FullyBootedEvent') {
                Log::info('Asterisk fully booted');
                return response()->json(['event' => 'FullyBooted']);
            }
        });

        $client->open();
        $res = $client->send($action);

        if ($res->getKey('response') === 'Success') {
            $startTime = time();
            $maxWaitTime = 60;

            while (!$hangup && (time() - $startTime < $maxWaitTime)) {
                $client->process();
            }

            if (time() - $startTime >= $maxWaitTime) {
                Log::warning("Call processing timed out after {$maxWaitTime} seconds");
                // $answered = false;
                $hangup = true;
            }
        }

        $client->close();

        if ($answered) {
            return response()->json(['message' => 'Call Answered and Completed'], 200);
        } else {
            return response()->json(['message' => 'Call Failed, Busy or Not Answered'], 201);
        }
    }
}
